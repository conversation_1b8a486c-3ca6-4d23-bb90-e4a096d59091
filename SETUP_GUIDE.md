# AJC Internship Platform - Setup Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Supabase account
- Git

### 1. Environment Setup

1. Clone the repository and install dependencies:
```bash
npm install
```

2. Create a Supabase project at [supabase.com](https://supabase.com)

3. Copy `.env.local` and update with your Supabase credentials:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_ADMIN_EMAIL=<EMAIL>
```

### 2. Database Setup

1. In your Supabase dashboard, go to SQL Editor
2. Run the `database-schema.sql` file to create all tables
3. Run the `database-seed.sql` file to populate with sample data

### 3. Authentication Setup

1. In Supabase dashboard, go to Authentication > Settings
2. Enable email authentication
3. Set up email templates (optional)
4. Configure redirect URLs for your domain

### 4. Storage Setup

1. In Supabase dashboard, go to Storage
2. Create buckets for:
   - `course-videos` (for video content)
   - `course-materials` (for PDFs and documents)
   - `task-submissions` (for student uploads)

### 5. Row Level Security (RLS)

The database schema includes RLS policies. Ensure they're enabled:
- Users can only see their own data
- Admins have full access
- Public access for certificate verification

## 🧪 Testing

### Admin Account
- Email: `<EMAIL>`
- Password: Create during signup
- Access: Full admin dashboard with all features

### Student Account
- Email: Any valid email except admin
- Password: Create during signup
- Access: Student dashboard with enrolled courses

### Sample Certificate IDs for Testing
- `AJC-DEMO001` - Power BI Gold Certificate
- `AJC-DEMO002` - Full Stack Silver Certificate
- `AJC-DEMO003` - Frontend Participation Certificate

## 🔧 Development

### Start Development Server
```bash
npm run dev
```

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

## 📱 Features Implemented

### ✅ Authentication System
- [x] Supabase Auth integration
- [x] Role-based access control
- [x] Protected routes
- [x] Admin/Student role assignment

### ✅ Admin Dashboard
- [x] Course management (CRUD)
- [x] User management
- [x] Task creation
- [x] MCQ creation
- [x] Certificate generation
- [x] Real-time data from Supabase

### ✅ Student Dashboard
- [x] Enrolled courses display
- [x] Task management
- [x] MCQ quizzes with scoring
- [x] Leaderboard system
- [x] Certificate viewing

### ✅ Certificate Verification
- [x] Public verification system
- [x] Database lookup
- [x] Sample certificates for testing

### ✅ UI/UX Enhancements
- [x] Sidebar navigation
- [x] Course cards with icons
- [x] Notification system
- [x] Mobile responsive design
- [x] shadcn/ui components

## 🚧 Future Enhancements

### Payment Integration
To add payment functionality:
1. Install Razorpay/Stripe SDK
2. Create payment routes
3. Add payment table to database
4. Implement enrollment after payment

### Video Streaming
To add video functionality:
1. Upload videos to Supabase Storage
2. Implement video player component
3. Add progress tracking
4. Create video management in admin

### Live Sessions
To add live sessions:
1. Integrate Zoom API
2. Create meeting scheduling
3. Add calendar component
4. Store meeting links in database

## 🐛 Troubleshooting

### Common Issues

1. **Supabase Connection Error**
   - Check environment variables
   - Verify Supabase URL and key
   - Ensure project is not paused

2. **Authentication Not Working**
   - Check RLS policies
   - Verify email confirmation settings
   - Check redirect URLs

3. **Database Queries Failing**
   - Verify table structure
   - Check RLS policies
   - Ensure user has proper permissions

### Support
For issues or questions, check:
- Supabase documentation
- React/Vite documentation
- shadcn/ui documentation

## 📦 Deployment

### Vercel Deployment
1. Connect GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Netlify Deployment
1. Connect GitHub repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Add environment variables

## 🔒 Security Notes

- Environment variables are properly configured
- RLS policies protect user data
- Admin access is restricted to specific email
- Certificate verification is public but secure
- All database operations use Supabase security
