import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useToast } from "@/hooks/use-toast";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/contexts/MockAuthContext";
import DashboardLayout from "@/components/DashboardLayout";
import CourseCard from "@/components/CourseCard";
import NotificationSystem from "@/components/NotificationSystem";
import {
  getStudentEnrollments,
  getCourseTasks,
  getCourseMCQs,
  submitMCQResult,
  getStudentResults,
  getStudentCertificates,
  getCourseLeaderboard
} from "@/lib/database";
import {
  BookOpen,
  Play,
  Download,
  CheckCircle,
  Clock,
  Trophy,
  Bell,
  LogOut,
  Star,
  Award,
  TrendingUp,
  Video,
  FileText,
  Users,
  Target,
  Loader2,
  Send
} from "lucide-react";

const StudentDashboard = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { signOut, userProfile, user } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTab = searchParams.get('tab') || 'courses';

  const [enrolledCourses, setEnrolledCourses] = useState<any[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [mcqs, setMCQs] = useState<any[]>([]);
  const [results, setResults] = useState<any[]>([]);
  const [certificates, setCertificates] = useState<any[]>([]);
  const [leaderboards, setLeaderboards] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<string>("");

  const [mcqAnswers, setMCQAnswers] = useState<{[key: string]: number}>({});
  const [taskSubmission, setTaskSubmission] = useState({
    task_id: "",
    submission_text: "",
    file_url: ""
  });

  // Fetch data on component mount
  useEffect(() => {
    if (user?.id) {
      fetchStudentData();
    }
  }, [user?.id]);

  const fetchStudentData = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // Fetch enrolled courses
      const { data: enrollments, error: enrollmentError } = await getStudentEnrollments(user.id);
      if (enrollmentError) {
        toast({
          title: "Error",
          description: "Failed to fetch enrolled courses",
          variant: "destructive",
        });
      } else {
        setEnrolledCourses(enrollments || []);

        // Fetch additional data for each course
        if (enrollments && enrollments.length > 0) {
          await Promise.all(enrollments.map(async (enrollment: any) => {
            const courseId = enrollment.course.id;

            // Fetch tasks, MCQs, and leaderboard for each course
            const [tasksResult, mcqsResult, leaderboardResult] = await Promise.all([
              getCourseTasks(courseId),
              getCourseMCQs(courseId),
              getCourseLeaderboard(courseId)
            ]);

            if (tasksResult.data) {
              setTasks(prev => [...prev, ...tasksResult.data]);
            }
            if (mcqsResult.data) {
              setMCQs(prev => [...prev, ...mcqsResult.data]);
            }
            if (leaderboardResult.data) {
              setLeaderboards(prev => ({...prev, [courseId]: leaderboardResult.data}));
            }
          }));
        }
      }

      // Fetch student results and certificates
      const [resultsResult, certificatesResult] = await Promise.all([
        getStudentResults(user.id),
        getStudentCertificates(user.id)
      ]);

      if (resultsResult.data) {
        setResults(resultsResult.data);
      }
      if (certificatesResult.data) {
        setCertificates(certificatesResult.data);
      }

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch student data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged Out",
        description: "You have been successfully logged out.",
      });
      navigate("/");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to logout",
        variant: "destructive",
      });
    }
  };

  const handleMCQSubmit = async (mcqId: string) => {
    if (!user?.id || mcqAnswers[mcqId] === undefined) {
      toast({
        title: "Error",
        description: "Please select an answer",
        variant: "destructive",
      });
      return;
    }

    setActionLoading(true);
    try {
      const mcq = mcqs.find(m => m.id === mcqId);
      const isCorrect = mcqAnswers[mcqId] === mcq?.correct_answer;
      const score = isCorrect ? 100 : 0;

      const { error } = await submitMCQResult(user.id, mcqId, score);

      if (error) {
        toast({
          title: "Error",
          description: "Failed to submit answer",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Answer Submitted!",
          description: `You scored ${score}%. ${isCorrect ? 'Correct!' : 'Incorrect, try again next time.'}`,
        });

        // Refresh results
        const { data: newResults } = await getStudentResults(user.id);
        if (newResults) {
          setResults(newResults);
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit answer",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleTaskSubmit = async () => {
    if (!taskSubmission.task_id || !taskSubmission.submission_text) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setActionLoading(true);
    try {
      // In a real implementation, you would save the task submission to the database
      // For now, we'll just show a success message
      toast({
        title: "Task Submitted!",
        description: "Your task submission has been recorded.",
      });

      setTaskSubmission({ task_id: "", submission_text: "", file_url: "" });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit task",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  const [recentActivities] = useState([
    { id: 1, activity: "Completed MCQ Quiz - React Hooks", score: "8/10", time: "2 hours ago" },
    { id: 2, activity: "Submitted Assignment - Database Design", score: "Pending", time: "1 day ago" },
    { id: 3, activity: "Attended Live Session - API Integration", score: "Present", time: "2 days ago" },
    { id: 4, activity: "Downloaded Notes - JavaScript Fundamentals", score: "Completed", time: "3 days ago" }
  ]);

  const enrollInCourse = () => {
    toast({
      title: "Enrollment Initiated",
      description: "Please complete payment to access the course.",
    });
  };

  return (
    <DashboardLayout>
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Student Dashboard</h1>
          <p className="text-sm text-muted-foreground">Welcome back, {userProfile?.name || 'Student'}!</p>
        </div>
        <div className="flex items-center space-x-4">
          <NotificationSystem />
        </div>
      </div>
        {/* Quick Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Enrolled Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{enrolledCourses.length}</div>
              <p className="text-xs text-muted-foreground">Active enrollments</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overall Progress</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {enrolledCourses.length > 0 ? Math.round((results.reduce((sum, result) => sum + result.score, 0) / results.length) || 0) : 0}%
              </div>
              <p className="text-xs text-muted-foreground">Average quiz score</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Certificates Earned</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{certificates.length}</div>
              <p className="text-xs text-muted-foreground">
                {certificates.length > 0 ? `${certificates[0]?.type} certificate` : 'No certificates yet'}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Tasks</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tasks.length}</div>
              <p className="text-xs text-muted-foreground">In Power BI course</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={(value) => setSearchParams({ tab: value })} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="courses">My Courses</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="quizzes">Quizzes</TabsTrigger>
            <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
            <TabsTrigger value="certificates">Certificates</TabsTrigger>
            <TabsTrigger value="enroll">Enroll More</TabsTrigger>
          </TabsList>

          {/* My Courses Tab */}
          <TabsContent value="courses" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">My Courses</h2>
            </div>

            <div className="grid gap-6">
              {enrolledCourses.map((enrollment) => {
                const course = enrollment.course;
                const courseTasks = tasks.filter(task => task.course_id === course.id);
                const courseResults = results.filter(result => result.mcq?.course_id === course.id);
                const averageScore = courseResults.length > 0 ?
                  Math.round(courseResults.reduce((sum, result) => sum + result.score, 0) / courseResults.length) : 0;
                const certificate = certificates.find(cert => cert.course_id === course.id);

                return (
                  <CourseCard
                    key={enrollment.id}
                    course={{
                      id: course.id,
                      name: course.name,
                      description: course.description,
                      progress: averageScore,
                      averageScore: averageScore,
                      tasksCount: courseTasks.length,
                      isEnrolled: true,
                      certificate: certificate ? { type: certificate.type } : undefined
                    }}
                    variant="enrolled"
                    onContinue={() => {
                      toast({
                        title: "Continue Learning",
                        description: "Redirecting to course content...",
                      });
                    }}
                    onViewLeaderboard={() => {
                      const leaderboard = leaderboards[course.id];
                      if (leaderboard) {
                        toast({
                          title: "Leaderboard",
                          description: `You are ranked #${leaderboard.find((entry: any) => entry.student_id === user?.id)?.rank || 'N/A'} in this course`,
                        });
                      }
                    }}
                  />
                );
              })}
              {enrolledCourses.length === 0 && (
                <Card>
                  <CardContent className="text-center py-8">
                    <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No courses enrolled yet. Browse available courses to get started.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Tasks Tab */}
          <TabsContent value="tasks" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">My Tasks</h2>
            </div>

            <div className="space-y-4">
              {tasks.map((task) => (
                <Card key={task.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{task.title}</CardTitle>
                        <CardDescription>
                          Due: {new Date(task.deadline).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <Badge variant={new Date(task.deadline) < new Date() ? "destructive" : "outline"}>
                        {new Date(task.deadline) < new Date() ? "Overdue" : "Pending"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">{task.description}</p>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="submission">Your Submission</Label>
                        <Textarea
                          id="submission"
                          placeholder="Enter your task submission here..."
                          value={taskSubmission.task_id === task.id ? taskSubmission.submission_text : ""}
                          onChange={(e) => setTaskSubmission({
                            ...taskSubmission,
                            task_id: task.id,
                            submission_text: e.target.value
                          })}
                          rows={4}
                        />
                      </div>
                      <Button
                        onClick={handleTaskSubmit}
                        disabled={actionLoading || taskSubmission.task_id !== task.id || !taskSubmission.submission_text}
                      >
                        {actionLoading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Send className="h-4 w-4 mr-2" />
                        )}
                        Submit Task
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {tasks.length === 0 && (
                <Card>
                  <CardContent className="text-center py-8">
                    <Target className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No pending tasks. Great job staying on top of your assignments!</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Quizzes Tab */}
          <TabsContent value="quizzes" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Course Quizzes</h2>
            </div>

            <div className="space-y-4">
              {mcqs.map((mcq) => {
                const hasAnswered = results.some(result => result.mcq_id === mcq.id);
                const userResult = results.find(result => result.mcq_id === mcq.id);

                return (
                  <Card key={mcq.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Quiz Question</CardTitle>
                        {hasAnswered && (
                          <Badge variant={userResult?.score === 100 ? "default" : "secondary"}>
                            Score: {userResult?.score}%
                          </Badge>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm mb-4">{mcq.question}</p>

                      {!hasAnswered ? (
                        <div className="space-y-4">
                          <RadioGroup
                            value={mcqAnswers[mcq.id]?.toString()}
                            onValueChange={(value) => setMCQAnswers({
                              ...mcqAnswers,
                              [mcq.id]: parseInt(value)
                            })}
                          >
                            {mcq.options.map((option: string, index: number) => (
                              <div key={index} className="flex items-center space-x-2">
                                <RadioGroupItem value={index.toString()} id={`${mcq.id}-${index}`} />
                                <Label htmlFor={`${mcq.id}-${index}`}>{option}</Label>
                              </div>
                            ))}
                          </RadioGroup>
                          <Button
                            onClick={() => handleMCQSubmit(mcq.id)}
                            disabled={actionLoading || mcqAnswers[mcq.id] === undefined}
                          >
                            {actionLoading ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <CheckCircle className="h-4 w-4 mr-2" />
                            )}
                            Submit Answer
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {mcq.options.map((option: string, index: number) => (
                            <div
                              key={index}
                              className={`p-2 rounded border ${
                                index === mcq.correct_answer ? 'bg-green-100 border-green-500' :
                                index === userResult?.mcq_id && userResult?.score === 0 ? 'bg-red-100 border-red-500' :
                                'bg-muted'
                              }`}
                            >
                              {option}
                              {index === mcq.correct_answer && <span className="ml-2 text-green-600">✓ Correct</span>}
                            </div>
                          ))}
                          <p className="text-sm text-muted-foreground mt-2">
                            You scored {userResult?.score}% on this quiz.
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
              {mcqs.length === 0 && (
                <Card>
                  <CardContent className="text-center py-8">
                    <CheckCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No quizzes available yet. Check back later for new assessments!</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Leaderboard Tab */}
          <TabsContent value="leaderboard" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Course Leaderboards</h2>
            </div>

            <div className="space-y-6">
              {enrolledCourses.map((enrollment) => {
                const course = enrollment.course;
                const leaderboard = leaderboards[course.id] || [];

                return (
                  <Card key={course.id}>
                    <CardHeader>
                      <CardTitle>{course.name} Leaderboard</CardTitle>
                      <CardDescription>Top performers in this course</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {leaderboard.slice(0, 10).map((entry: any, index: number) => (
                          <div
                            key={entry.student_id}
                            className={`flex items-center justify-between p-3 rounded-lg ${
                              entry.student_id === user?.id ? 'bg-primary/10 border border-primary/20' : 'bg-muted/50'
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                index === 0 ? 'bg-yellow-500 text-white' :
                                index === 1 ? 'bg-gray-400 text-white' :
                                index === 2 ? 'bg-amber-600 text-white' :
                                'bg-muted text-muted-foreground'
                              }`}>
                                {entry.rank}
                              </div>
                              <div>
                                <p className="font-medium">{entry.student_name}</p>
                                <p className="text-sm text-muted-foreground">
                                  {entry.quiz_count} quizzes completed
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold">{entry.total_score} pts</p>
                              <p className="text-sm text-muted-foreground">{entry.average_score}% avg</p>
                            </div>
                          </div>
                        ))}
                        {leaderboard.length === 0 && (
                          <p className="text-center text-muted-foreground py-4">
                            No leaderboard data available yet. Complete some quizzes to see rankings!
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Certificates Tab */}
          <TabsContent value="certificates" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">My Certificates</h2>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {certificates.map((certificate) => (
                <Card key={certificate.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{certificate.course?.name}</CardTitle>
                        <CardDescription>
                          Issued on {new Date(certificate.issue_date).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <Badge
                        variant={
                          certificate.type === 'gold' ? 'default' :
                          certificate.type === 'silver' ? 'secondary' : 'outline'
                        }
                        className={
                          certificate.type === 'gold' ? 'bg-yellow-500' :
                          certificate.type === 'silver' ? 'bg-gray-400' : ''
                        }
                      >
                        {certificate.type.charAt(0).toUpperCase() + certificate.type.slice(1)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center p-6 border-2 border-dashed border-muted rounded-lg">
                        <Award className="h-12 w-12 mx-auto text-primary mb-2" />
                        <p className="font-medium">Certificate of {certificate.type.charAt(0).toUpperCase() + certificate.type.slice(1)}</p>
                        <p className="text-sm text-muted-foreground">ID: {certificate.unique_id}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button className="flex-1">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        <Button variant="outline" className="flex-1" onClick={() => navigate(`/verify?id=${certificate.unique_id}`)}>
                          <Award className="h-4 w-4 mr-2" />
                          Verify
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {certificates.length === 0 && (
                <Card className="md:col-span-2">
                  <CardContent className="text-center py-8">
                    <Award className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No certificates earned yet. Complete courses to earn certificates!</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Enroll More Tab */}
          <TabsContent value="enroll" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Available Courses</h2>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { title: "Frontend Development", price: "₹5,999", duration: "10 weeks", icon: "🎨" },
                { title: "Backend Development", price: "₹5,999", duration: "10 weeks", icon: "⚙️" },
                { title: "Database Management", price: "₹4,499", duration: "6 weeks", icon: "🗄️" },
                { title: "Flutter Development", price: "₹6,499", duration: "10 weeks", icon: "📱" }
              ].map((course, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="text-3xl mb-2">{course.icon}</div>
                    <CardTitle>{course.title}</CardTitle>
                    <CardDescription>{course.duration} • {course.price}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button onClick={enrollInCourse} className="w-full">
                      Enroll Now
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
    </DashboardLayout>
  );
};

export default StudentDashboard;