import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import {
  BookOpen,
  Users,
  Award,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  Target,
  Trophy,
  FileText,
  CheckCircle,
  UserCheck,
  Shield
} from 'lucide-react'

interface SidebarProps {
  className?: string
}

const Sidebar = ({ className }: SidebarProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const location = useLocation()
  const { userProfile, isAdminUser, signOut } = useAuth()

  const adminNavItems = [
    { icon: Home, label: 'Dashboard', href: '/admin', badge: null },
    { icon: BookOpen, label: 'Courses', href: '/admin?tab=courses', badge: null },
    { icon: UserCheck, label: 'Faculty', href: '/admin?tab=faculty', badge: null },
    { icon: Users, label: 'Students', href: '/admin?tab=students', badge: null },
    { icon: Target, label: 'Tasks', href: '/admin?tab=tasks', badge: null },
    { icon: CheckCircle, label: 'MCQs', href: '/admin?tab=mcqs', badge: null },
    { icon: Award, label: 'Certificates', href: '/admin?tab=certificates', badge: null },
  ]

  const studentNavItems = [
    { icon: Home, label: 'Dashboard', href: '/student', badge: null },
    { icon: BookOpen, label: 'My Courses', href: '/student?tab=courses', badge: null },
    { icon: Target, label: 'Tasks', href: '/student?tab=tasks', badge: '3' },
    { icon: CheckCircle, label: 'Quizzes', href: '/student?tab=quizzes', badge: null },
    { icon: Trophy, label: 'Leaderboard', href: '/student?tab=leaderboard', badge: null },
    { icon: Award, label: 'Certificates', href: '/student?tab=certificates', badge: null },
  ]

  const navItems = isAdminUser ? adminNavItems : studentNavItems

  const handleLogout = async () => {
    await signOut()
  }

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex h-16 items-center border-b px-4">
        <Link to="/" className="flex items-center space-x-3">
          <img
            src="/ajc-logo.png"
            alt="AJC Internship Platform"
            className="h-10 w-auto object-contain"
          />
          {!isCollapsed && (
            <span className="font-bold text-lg text-foreground">AJC INTERNSHIP</span>
          )}
        </Link>
      </div>

      {/* User Info */}
      {!isCollapsed && (
        <div className="border-b p-4">
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
              <span className="text-sm font-medium text-primary-foreground">
                {userProfile?.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{userProfile?.name || 'User'}</p>
              <p className="text-xs text-muted-foreground capitalize">{userProfile?.role || 'Student'}</p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navItems.map((item) => {
          const isActive = location.pathname === item.href.split('?')[0]
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-muted hover:text-foreground'
              )}
            >
              <item.icon className="h-4 w-4" />
              {!isCollapsed && (
                <>
                  <span className="flex-1">{item.label}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="ml-auto">
                      {item.badge}
                    </Badge>
                  )}
                </>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="border-t p-4">
        <Button
          variant="ghost"
          className="w-full justify-start"
          onClick={handleLogout}
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span className="ml-3">Logout</span>}
        </Button>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile Toggle */}
      <Button
        variant="ghost"
        size="sm"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
      </Button>

      {/* Mobile Sidebar */}
      {isMobileOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div className="fixed inset-0 bg-black/50" onClick={() => setIsMobileOpen(false)} />
          <div className="fixed left-0 top-0 h-full w-64 bg-background border-r">
            <SidebarContent />
          </div>
        </div>
      )}

      {/* Desktop Sidebar */}
      <div
        className={cn(
          'hidden md:flex h-screen bg-background border-r transition-all duration-300',
          isCollapsed ? 'w-16' : 'w-64',
          className
        )}
      >
        <div className="flex flex-col w-full">
          {/* Collapse Toggle */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-4 right-2 z-10"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
          </Button>
          
          <SidebarContent />
        </div>
      </div>
    </>
  )
}

export default Sidebar
