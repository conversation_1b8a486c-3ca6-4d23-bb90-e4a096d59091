import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/contexts/MockAuthContext";
import DashboardLayout from "@/components/DashboardLayout";
import CourseCard from "@/components/CourseCard";
import NotificationSystem from "@/components/NotificationSystem";
import {
  getCourses,
  createCourse,
  updateCourse,
  deleteCourse as deleteCourseDB,
  getAllUsers,
  updateUserRole,
  createCertificate,
  createTask,
  createMCQ
} from "@/lib/mockDatabase";
import {
  Users,
  BookOpen,
  Award,
  CreditCard,
  Plus,
  Edit3,
  Trash2,
  LogOut,
  UserCheck,
  TrendingUp,
  FileText,
  Upload,
  Loader2
} from "lucide-react";

const AdminDashboard = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { signOut, userProfile } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTab = searchParams.get('tab') || 'overview';

  const [courses, setCourses] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  const [newCourse, setNewCourse] = useState({
    name: "",
    price: "",
    description: "",
    faculty_id: ""
  });

  const [newTask, setNewTask] = useState({
    course_id: "",
    title: "",
    description: "",
    deadline: ""
  });

  const [newMCQ, setNewMCQ] = useState({
    course_id: "",
    question: "",
    options: ["", "", "", ""],
    correct_answer: 0
  });

  const [newCertificate, setNewCertificate] = useState({
    student_id: "",
    course_id: "",
    type: "participation" as "gold" | "silver" | "participation"
  });

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [coursesResult, usersResult] = await Promise.all([
        getCourses(),
        getAllUsers()
      ]);

      if (coursesResult.error) {
        toast({
          title: "Error",
          description: "Failed to fetch courses",
          variant: "destructive",
        });
      } else {
        setCourses(coursesResult.data || []);
      }

      if (usersResult.error) {
        toast({
          title: "Error",
          description: "Failed to fetch users",
          variant: "destructive",
        });
      } else {
        setUsers(usersResult.data || []);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged Out",
        description: "You have been successfully logged out.",
      });
      navigate("/");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to logout",
        variant: "destructive",
      });
    }
  };

  const handleAddCourse = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newCourse.name && newCourse.price) {
      setActionLoading(true);
      try {
        const { data, error } = await createCourse({
          name: newCourse.name,
          description: newCourse.description,
          price: parseFloat(newCourse.price),
          faculty_id: newCourse.faculty_id || null
        });

        if (error) {
          toast({
            title: "Error",
            description: "Failed to create course",
            variant: "destructive",
          });
        } else {
          setCourses([...courses, data]);
          setNewCourse({ name: "", price: "", description: "", faculty_id: "" });
          toast({
            title: "Course Added",
            description: `${newCourse.name} has been added successfully.`,
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to create course",
          variant: "destructive",
        });
      } finally {
        setActionLoading(false);
      }
    }
  };

  const handleDeleteCourse = async (id: string) => {
    setActionLoading(true);
    try {
      const { error } = await deleteCourseDB(id);

      if (error) {
        toast({
          title: "Error",
          description: "Failed to delete course",
          variant: "destructive",
        });
      } else {
        setCourses(courses.filter(course => course.id !== id));
        toast({
          title: "Course Deleted",
          description: "Course has been removed successfully.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete course",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdateUserRole = async (userId: string, role: 'admin' | 'student' | 'faculty') => {
    setActionLoading(true);
    try {
      const { error } = await updateUserRole(userId, role);

      if (error) {
        toast({
          title: "Error",
          description: "Failed to update user role",
          variant: "destructive",
        });
      } else {
        setUsers(users.map(user =>
          user.id === userId ? { ...user, role } : user
        ));
        toast({
          title: "Role Updated",
          description: "User role has been updated successfully.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleAddTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newTask.course_id && newTask.title && newTask.deadline) {
      setActionLoading(true);
      try {
        const { error } = await createTask({
          course_id: newTask.course_id,
          title: newTask.title,
          description: newTask.description,
          deadline: newTask.deadline
        });

        if (error) {
          toast({
            title: "Error",
            description: "Failed to create task",
            variant: "destructive",
          });
        } else {
          setNewTask({ course_id: "", title: "", description: "", deadline: "" });
          toast({
            title: "Task Added",
            description: "Task has been created successfully.",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to create task",
          variant: "destructive",
        });
      } finally {
        setActionLoading(false);
      }
    }
  };

  const handleAddMCQ = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newMCQ.course_id && newMCQ.question && newMCQ.options.every(opt => opt.trim())) {
      setActionLoading(true);
      try {
        const { error } = await createMCQ({
          course_id: newMCQ.course_id,
          question: newMCQ.question,
          options: newMCQ.options,
          correct_answer: newMCQ.correct_answer
        });

        if (error) {
          toast({
            title: "Error",
            description: "Failed to create MCQ",
            variant: "destructive",
          });
        } else {
          setNewMCQ({
            course_id: "",
            question: "",
            options: ["", "", "", ""],
            correct_answer: 0
          });
          toast({
            title: "MCQ Added",
            description: "MCQ has been created successfully.",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to create MCQ",
          variant: "destructive",
        });
      } finally {
        setActionLoading(false);
      }
    }
  };

  const handleGenerateCertificate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newCertificate.student_id && newCertificate.course_id) {
      setActionLoading(true);
      try {
        const { error } = await createCertificate({
          student_id: newCertificate.student_id,
          course_id: newCertificate.course_id,
          type: newCertificate.type
        });

        if (error) {
          toast({
            title: "Error",
            description: "Failed to generate certificate",
            variant: "destructive",
          });
        } else {
          setNewCertificate({ student_id: "", course_id: "", type: "participation" });
          toast({
            title: "Certificate Generated",
            description: "Certificate has been generated successfully.",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to generate certificate",
          variant: "destructive",
        });
      } finally {
        setActionLoading(false);
      }
    }
  };

  // Calculate stats
  const students = users.filter(user => user.role === 'student');
  const faculty = users.filter(user => user.role === 'faculty');
  const totalStudents = students.length;
  const totalRevenue = courses.reduce((sum, course) => sum + (course.price * 10), 0); // Estimated revenue

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Admin Dashboard</h1>
          <p className="text-sm text-muted-foreground">Welcome back, {userProfile?.name || 'Admin'}</p>
        </div>
        <div className="flex items-center space-x-4">
          <NotificationSystem />
        </div>
      </div>
        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalStudents}</div>
              <p className="text-xs text-muted-foreground">Across all courses</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{courses.length}</div>
              <p className="text-xs text-muted-foreground">Available programs</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{(totalRevenue / 100000).toFixed(1)}L</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Faculty Members</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{faculty.length}</div>
              <p className="text-xs text-muted-foreground">Active instructors</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={(value) => setSearchParams({ tab: value })} className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="courses">Courses</TabsTrigger>
            <TabsTrigger value="faculty">Faculty</TabsTrigger>
            <TabsTrigger value="students">Students</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="mcqs">MCQs</TabsTrigger>
            <TabsTrigger value="certificates">Certificates</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Latest platform activities and updates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4 p-3 border rounded-lg">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">5 new students enrolled today</p>
                        <p className="text-xs text-muted-foreground">Power BI and Full Stack Development courses</p>
                      </div>
                      <span className="text-xs text-muted-foreground">2 hours ago</span>
                    </div>
                    <div className="flex items-center space-x-4 p-3 border rounded-lg">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">3 certificates generated</p>
                        <p className="text-xs text-muted-foreground">Students completed Frontend Development</p>
                      </div>
                      <span className="text-xs text-muted-foreground">4 hours ago</span>
                    </div>
                    <div className="flex items-center space-x-4 p-3 border rounded-lg">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">New task assignments created</p>
                        <p className="text-xs text-muted-foreground">React component library project</p>
                      </div>
                      <span className="text-xs text-muted-foreground">1 day ago</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Course Management</h2>
            </div>
            
            {/* Add Course Form */}
            <Card>
              <CardHeader>
                <CardTitle>Add New Course</CardTitle>
                <CardDescription>Create a new course for students to enroll</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddCourse} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="courseName">Course Name</Label>
                      <Input
                        id="courseName"
                        value={newCourse.name}
                        onChange={(e) => setNewCourse({...newCourse, name: e.target.value})}
                        placeholder="e.g., React Development"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="coursePrice">Price (₹)</Label>
                      <Input
                        id="coursePrice"
                        type="number"
                        value={newCourse.price}
                        onChange={(e) => setNewCourse({...newCourse, price: e.target.value})}
                        placeholder="4999"
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="courseDescription">Description</Label>
                      <Textarea
                        id="courseDescription"
                        value={newCourse.description}
                        onChange={(e) => setNewCourse({...newCourse, description: e.target.value})}
                        placeholder="Course description..."
                        rows={3}
                      />
                    </div>
                    <div>
                      <Label htmlFor="facultySelect">Assign Faculty (Optional)</Label>
                      <Select value={newCourse.faculty_id} onValueChange={(value) => setNewCourse({...newCourse, faculty_id: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select faculty member" />
                        </SelectTrigger>
                        <SelectContent>
                          {faculty.map((member) => (
                            <SelectItem key={member.id} value={member.id}>
                              {member.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <Button type="submit" disabled={actionLoading}>
                    {actionLoading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Add Course
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Courses List */}
            <div className="grid gap-6">
              {courses.map((course) => (
                <CourseCard
                  key={course.id}
                  course={{
                    id: course.id,
                    name: course.name,
                    description: course.description,
                    price: course.price,
                  }}
                  variant="admin"
                  showActions={false}
                />
              ))}
              {courses.length === 0 && (
                <Card>
                  <CardContent className="text-center py-8">
                    <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No courses available. Create your first course above.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Faculty Tab */}
          <TabsContent value="faculty" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Faculty Management</h2>
            </div>

            {/* Faculty List */}
            <div className="grid gap-4">
              {faculty.map((member) => (
                <Card key={member.id}>
                  <CardContent className="flex items-center justify-between p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <UserCheck className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{member.name}</h3>
                        <p className="text-sm text-muted-foreground">{member.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge variant="outline">Faculty</Badge>
                      <Select
                        value={member.role}
                        onValueChange={(value) => handleUpdateUserRole(member.id, value as 'admin' | 'student' | 'faculty')}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="faculty">Faculty</SelectItem>
                          <SelectItem value="student">Student</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {faculty.length === 0 && (
                <Card>
                  <CardContent className="text-center py-8">
                    <UserCheck className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No faculty members found. Users can be promoted to faculty role.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Students Tab */}
          <TabsContent value="students" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Student Management</h2>
            </div>

            <div className="grid gap-4">
              {students.map((student) => (
                <Card key={student.id}>
                  <CardContent className="flex items-center justify-between p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">{student.name.charAt(0)}</span>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{student.name}</h3>
                        <p className="text-sm text-muted-foreground">{student.email}</p>
                        <p className="text-sm text-muted-foreground">Joined: {new Date(student.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge variant="outline">{student.role}</Badge>
                      <Select
                        value={student.role}
                        onValueChange={(value) => handleUpdateUserRole(student.id, value as 'admin' | 'student' | 'faculty')}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="student">Student</SelectItem>
                          <SelectItem value="faculty">Faculty</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {students.length === 0 && (
                <Card>
                  <CardContent className="text-center py-8">
                    <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No students found. Students will appear here after registration.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Tasks Tab */}
          <TabsContent value="tasks" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Task Management</h2>
            </div>

            {/* Add Task Form */}
            <Card>
              <CardHeader>
                <CardTitle>Create New Task</CardTitle>
                <CardDescription>Assign tasks to students in specific courses</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddTask} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="taskCourse">Course</Label>
                      <Select value={newTask.course_id} onValueChange={(value) => setNewTask({...newTask, course_id: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select course" />
                        </SelectTrigger>
                        <SelectContent>
                          {courses.map((course) => (
                            <SelectItem key={course.id} value={course.id}>
                              {course.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="taskDeadline">Deadline</Label>
                      <Input
                        id="taskDeadline"
                        type="datetime-local"
                        value={newTask.deadline}
                        onChange={(e) => setNewTask({...newTask, deadline: e.target.value})}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="taskTitle">Task Title</Label>
                    <Input
                      id="taskTitle"
                      value={newTask.title}
                      onChange={(e) => setNewTask({...newTask, title: e.target.value})}
                      placeholder="e.g., Build a React Component"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="taskDescription">Description</Label>
                    <Textarea
                      id="taskDescription"
                      value={newTask.description}
                      onChange={(e) => setNewTask({...newTask, description: e.target.value})}
                      placeholder="Detailed task instructions..."
                      rows={4}
                    />
                  </div>
                  <Button type="submit" disabled={actionLoading}>
                    {actionLoading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Create Task
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* MCQs Tab */}
          <TabsContent value="mcqs" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">MCQ Management</h2>
            </div>

            {/* Add MCQ Form */}
            <Card>
              <CardHeader>
                <CardTitle>Create New MCQ</CardTitle>
                <CardDescription>Create multiple choice questions for course assessments</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddMCQ} className="space-y-4">
                  <div>
                    <Label htmlFor="mcqCourse">Course</Label>
                    <Select value={newMCQ.course_id} onValueChange={(value) => setNewMCQ({...newMCQ, course_id: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select course" />
                      </SelectTrigger>
                      <SelectContent>
                        {courses.map((course) => (
                          <SelectItem key={course.id} value={course.id}>
                            {course.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="mcqQuestion">Question</Label>
                    <Textarea
                      id="mcqQuestion"
                      value={newMCQ.question}
                      onChange={(e) => setNewMCQ({...newMCQ, question: e.target.value})}
                      placeholder="Enter your question here..."
                      rows={3}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Options</Label>
                    {newMCQ.options.map((option, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input
                          value={option}
                          onChange={(e) => {
                            const newOptions = [...newMCQ.options];
                            newOptions[index] = e.target.value;
                            setNewMCQ({...newMCQ, options: newOptions});
                          }}
                          placeholder={`Option ${index + 1}`}
                          required
                        />
                        <Button
                          type="button"
                          variant={newMCQ.correct_answer === index ? "default" : "outline"}
                          size="sm"
                          onClick={() => setNewMCQ({...newMCQ, correct_answer: index})}
                        >
                          {newMCQ.correct_answer === index ? "Correct" : "Mark Correct"}
                        </Button>
                      </div>
                    ))}
                  </div>
                  <Button type="submit" disabled={actionLoading}>
                    {actionLoading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Create MCQ
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Certificates Tab */}
          <TabsContent value="certificates" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Certificate Management</h2>
            </div>

            {/* Generate Certificate Form */}
            <Card>
              <CardHeader>
                <CardTitle>Generate Certificate</CardTitle>
                <CardDescription>Issue certificates to students upon course completion</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleGenerateCertificate} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="certStudent">Student</Label>
                      <Select value={newCertificate.student_id} onValueChange={(value) => setNewCertificate({...newCertificate, student_id: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select student" />
                        </SelectTrigger>
                        <SelectContent>
                          {students.map((student) => (
                            <SelectItem key={student.id} value={student.id}>
                              {student.name} ({student.email})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="certCourse">Course</Label>
                      <Select value={newCertificate.course_id} onValueChange={(value) => setNewCertificate({...newCertificate, course_id: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select course" />
                        </SelectTrigger>
                        <SelectContent>
                          {courses.map((course) => (
                            <SelectItem key={course.id} value={course.id}>
                              {course.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="certType">Certificate Type</Label>
                      <Select value={newCertificate.type} onValueChange={(value) => setNewCertificate({...newCertificate, type: value as "gold" | "silver" | "participation"})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gold">Gold</SelectItem>
                          <SelectItem value="silver">Silver</SelectItem>
                          <SelectItem value="participation">Participation</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <Button type="submit" disabled={actionLoading}>
                    {actionLoading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Award className="h-4 w-4 mr-2" />
                    )}
                    Generate Certificate
                  </Button>
                </form>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-3 gap-6">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle>Gold Certificates</CardTitle>
                  <CardDescription>Outstanding Performance (90%+ score)</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-yellow-600 mb-2">12</div>
                  <p className="text-sm text-muted-foreground">Issued this month</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-gray-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle>Silver Certificates</CardTitle>
                  <CardDescription>Good Performance (70-89% score)</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-600 mb-2">8</div>
                  <p className="text-sm text-muted-foreground">Issued this month</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle>Participation</CardTitle>
                  <CardDescription>Course Completion (50-69% score)</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600 mb-2">15</div>
                  <p className="text-sm text-muted-foreground">Issued this month</p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Certificates */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Certificate Approvals</CardTitle>
                <CardDescription>Students pending certificate approval</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {students.filter(s => s.certificate === "Pending").map((student) => (
                    <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-sm">{student.name.charAt(0)}</span>
                        </div>
                        <div>
                          <h4 className="font-semibold">{student.name}</h4>
                          <p className="text-sm text-muted-foreground">{student.course} - {student.progress}% complete</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          size="sm" 
                          className="bg-yellow-500 hover:bg-yellow-600"
                          onClick={() => toast({ title: "Gold Certificate Approved", description: `${student.name} has been awarded a Gold certificate.` })}
                        >
                          Gold
                        </Button>
                        <Button 
                          size="sm" 
                          variant="secondary"
                          onClick={() => toast({ title: "Silver Certificate Approved", description: `${student.name} has been awarded a Silver certificate.` })}
                        >
                          Silver
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => toast({ title: "Participation Certificate Approved", description: `${student.name} has been awarded a Participation certificate.` })}
                        >
                          Participation
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
    </DashboardLayout>
  );
};

export default AdminDashboard;