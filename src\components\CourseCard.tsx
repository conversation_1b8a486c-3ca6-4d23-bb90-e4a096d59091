import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Video, 
  FileText, 
  BarChart3, 
  Trophy, 
  Play, 
  Download, 
  Users, 
  Clock,
  CheckCircle,
  Target
} from 'lucide-react'

interface CourseCardProps {
  course: {
    id: string
    name: string
    description?: string
    price?: number
    progress?: number
    averageScore?: number
    tasksCount?: number
    videosCount?: number
    notesCount?: number
    isEnrolled?: boolean
    certificate?: {
      type: string
    }
  }
  onEnroll?: () => void
  onContinue?: () => void
  onViewTasks?: () => void
  onViewLeaderboard?: () => void
  showActions?: boolean
  variant?: 'enrolled' | 'available' | 'admin'
}

const CourseCard = ({ 
  course, 
  onEnroll, 
  onContinue, 
  onViewTasks, 
  onViewLeaderboard,
  showActions = true,
  variant = 'available' 
}: CourseCardProps) => {
  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 60) return 'bg-yellow-500'
    return 'bg-blue-500'
  }

  const getCertificateBadge = (type: string) => {
    const colors = {
      gold: 'bg-yellow-500 text-white',
      silver: 'bg-gray-400 text-white',
      participation: 'bg-blue-500 text-white'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-500 text-white'
  }

  return (
    <Card className="hover:shadow-lg transition-all duration-300 group">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl group-hover:text-primary transition-colors">
              {course.name}
            </CardTitle>
            <CardDescription className="mt-2">
              {course.description || 'No description available'}
            </CardDescription>
            {course.price && variant === 'available' && (
              <div className="mt-2">
                <Badge variant="secondary" className="text-lg font-semibold">
                  ₹{course.price.toLocaleString()}
                </Badge>
              </div>
            )}
          </div>
          {course.certificate && (
            <Badge className={getCertificateBadge(course.certificate.type)}>
              {course.certificate.type.charAt(0).toUpperCase() + course.certificate.type.slice(1)} Certificate
            </Badge>
          )}
        </div>

        {/* Progress for enrolled courses */}
        {variant === 'enrolled' && course.progress !== undefined && (
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-2">
              <span>Progress</span>
              <span>{course.progress}%</span>
            </div>
            <Progress value={course.progress} className="h-2" />
          </div>
        )}

        {/* Average score for enrolled courses */}
        {variant === 'enrolled' && course.averageScore !== undefined && (
          <div className="mt-2">
            <div className="flex justify-between text-sm">
              <span>Average Quiz Score</span>
              <span className="font-medium">{course.averageScore}%</span>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {/* Course Features Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          {/* Videos */}
          <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted transition-colors cursor-pointer">
            <Video className="h-6 w-6 text-primary mb-2" />
            <span className="text-sm font-medium">Videos</span>
            <span className="text-xs text-muted-foreground">
              {course.videosCount || 'Available'}
            </span>
          </div>

          {/* Notes */}
          <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted transition-colors cursor-pointer">
            <FileText className="h-6 w-6 text-green-600 mb-2" />
            <span className="text-sm font-medium">Notes</span>
            <span className="text-xs text-muted-foreground">
              {course.notesCount || 'Download'}
            </span>
          </div>

          {/* Tasks */}
          <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted transition-colors cursor-pointer">
            <Target className="h-6 w-6 text-orange-600 mb-2" />
            <span className="text-sm font-medium">Tasks</span>
            <span className="text-xs text-muted-foreground">
              {course.tasksCount || 0} pending
            </span>
          </div>

          {/* Leaderboard */}
          <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted transition-colors cursor-pointer">
            <Trophy className="h-6 w-6 text-yellow-600 mb-2" />
            <span className="text-sm font-medium">Leaderboard</span>
            <span className="text-xs text-muted-foreground">Rankings</span>
          </div>
        </div>

        {/* Action Buttons */}
        {showActions && (
          <div className="flex gap-3">
            {variant === 'enrolled' ? (
              <>
                <Button className="flex-1" onClick={onContinue}>
                  <Play className="h-4 w-4 mr-2" />
                  Continue Learning
                </Button>
                <Button variant="outline" onClick={onViewLeaderboard}>
                  <Trophy className="h-4 w-4 mr-2" />
                  Leaderboard
                </Button>
              </>
            ) : variant === 'available' ? (
              <Button className="w-full" onClick={onEnroll}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Enroll Now
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  {Math.floor(Math.random() * 100) + 50} Students
                </Button>
                <Button variant="outline" size="sm">
                  <Clock className="h-4 w-4 mr-2" />
                  Active
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default CourseCard
