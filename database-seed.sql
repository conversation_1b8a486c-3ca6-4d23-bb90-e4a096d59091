-- Insert sample courses
INSERT INTO courses (id, name, description, price, faculty_id) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Power BI', 'Master data visualization and business intelligence with Microsoft Power BI. Learn to create interactive dashboards and reports.', 4999.00, NULL),
('550e8400-e29b-41d4-a716-446655440002', 'Full Stack Development', 'Complete web development course covering frontend and backend technologies including React, Node.js, and databases.', 7999.00, NULL),
('550e8400-e29b-41d4-a716-446655440003', 'Frontend Development', 'Learn modern frontend development with React, HTML5, CSS3, and JavaScript. Build responsive and interactive web applications.', 5999.00, NULL),
('550e8400-e29b-41d4-a716-446655440004', 'Backend Development', 'Master server-side development with Node.js, Express, databases, and API development. Build scalable backend systems.', 5999.00, NULL),
('550e8400-e29b-41d4-a716-446655440005', 'Database Management', 'Comprehensive course on database design, SQL, NoSQL, and database administration. Learn MySQL, PostgreSQL, and MongoDB.', 4499.00, NULL),
('550e8400-e29b-41d4-a716-446655440006', 'Flutter Development', 'Build cross-platform mobile applications with Flutter and Dart. Create apps for iOS and Android from a single codebase.', 6499.00, NULL);

-- Insert sample tasks for courses
INSERT INTO tasks (id, course_id, title, description, deadline) VALUES
-- Power BI Tasks
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Create Your First Dashboard', 'Build a basic dashboard using sample sales data. Include at least 3 different visualizations.', '2024-09-15 23:59:59'),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'Data Modeling Exercise', 'Create relationships between multiple data tables and build a comprehensive data model.', '2024-09-20 23:59:59'),

-- Full Stack Development Tasks
('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 'Build a Todo App', 'Create a full-stack todo application with React frontend and Node.js backend.', '2024-09-25 23:59:59'),
('650e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', 'API Integration Project', 'Build a web app that consumes external APIs and displays data in an interactive format.', '2024-09-30 23:59:59'),

-- Frontend Development Tasks
('650e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440003', 'Responsive Portfolio Website', 'Create a responsive portfolio website using HTML, CSS, and JavaScript.', '2024-09-18 23:59:59'),
('650e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440003', 'React Component Library', 'Build a reusable component library with at least 5 different components.', '2024-09-22 23:59:59');

-- Insert sample MCQs for courses
INSERT INTO mcqs (id, course_id, question, options, correct_answer) VALUES
-- Power BI MCQs
('750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'What is the primary purpose of Power BI?', '["Data entry", "Data visualization and business intelligence", "Web development", "Database management"]', 1),
('750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'Which file format is commonly used to import data into Power BI?', '["PDF", "Excel (.xlsx)", "Word (.docx)", "PowerPoint (.pptx)"]', 1),

-- Full Stack Development MCQs
('750e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 'What does API stand for?', '["Application Programming Interface", "Advanced Programming Integration", "Automated Program Instruction", "Application Process Integration"]', 0),
('750e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', 'Which database is commonly used with Node.js?', '["Oracle", "MongoDB", "SQLite", "All of the above"]', 3),

-- Frontend Development MCQs
('750e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440003', 'What does CSS stand for?', '["Computer Style Sheets", "Cascading Style Sheets", "Creative Style Sheets", "Colorful Style Sheets"]', 1),
('750e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440003', 'Which JavaScript framework is developed by Facebook?', '["Angular", "Vue.js", "React", "Svelte"]', 2);

-- Note: Users, enrollments, results, and certificates will be created when users sign up and interact with the system
-- The admin user will be created <NAME_EMAIL> signs up

-- Insert sample certificates (these would normally be generated after course completion)
-- We'll add these as examples for the verification system
INSERT INTO certificates (id, student_id, course_id, type, unique_id, issue_date) VALUES
('850e8400-e29b-41d4-a716-446655440001', '450e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'gold', 'AJC-DEMO001', '2024-08-15 10:00:00'),
('850e8400-e29b-41d4-a716-446655440002', '450e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', 'silver', 'AJC-DEMO002', '2024-08-20 14:30:00'),
('850e8400-e29b-41d4-a716-446655440003', '450e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', 'participation', 'AJC-DEMO003', '2024-08-25 16:45:00');

-- Note: The student_ids in certificates reference users that would need to exist
-- In a real scenario, these would be created when actual users sign up and complete courses
