// Mock database functions for testing without Supabase

// Mock data storage keys
const COURSES_KEY = 'ajc_mock_courses'
const TASKS_KEY = 'ajc_mock_tasks'
const MCQS_KEY = 'ajc_mock_mcqs'
const RESULTS_KEY = 'ajc_mock_results'
const CERTIFICATES_KEY = 'ajc_mock_certificates'
const USERS_KEY = 'ajc_mock_users'

// Initialize mock data
const initializeMockData = () => {
  // Sample courses
  if (!localStorage.getItem(COURSES_KEY)) {
    const courses = [
      {
        id: 'course-1',
        name: 'Power BI Mastery',
        description: 'Learn advanced Power BI techniques for data visualization and business intelligence.',
        price: 15000,
        faculty_id: 'faculty-1',
        created_at: new Date().toISOString()
      },
      {
        id: 'course-2',
        name: 'Full Stack Development',
        description: 'Complete web development course covering React, Node.js, and databases.',
        price: 25000,
        faculty_id: 'faculty-1',
        created_at: new Date().toISOString()
      },
      {
        id: 'course-3',
        name: 'Frontend Development',
        description: 'Master modern frontend technologies including React, TypeScript, and CSS.',
        price: 18000,
        faculty_id: 'faculty-1',
        created_at: new Date().toISOString()
      }
    ]
    localStorage.setItem(COURSES_KEY, JSON.stringify(courses))
  }

  // Sample tasks
  if (!localStorage.getItem(TASKS_KEY)) {
    const tasks = [
      {
        id: 'task-1',
        course_id: 'course-1',
        title: 'Create Power BI Dashboard',
        description: 'Build a comprehensive dashboard using the provided dataset.',
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString()
      },
      {
        id: 'task-2',
        course_id: 'course-2',
        title: 'Build React Component Library',
        description: 'Create reusable React components with TypeScript.',
        deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString()
      }
    ]
    localStorage.setItem(TASKS_KEY, JSON.stringify(tasks))
  }

  // Sample MCQs
  if (!localStorage.getItem(MCQS_KEY)) {
    const mcqs = [
      {
        id: 'mcq-1',
        course_id: 'course-1',
        question: 'What is the primary purpose of Power BI?',
        options: ['Data Storage', 'Data Visualization', 'Data Entry', 'Data Backup'],
        correct_answer: 1,
        created_at: new Date().toISOString()
      },
      {
        id: 'mcq-2',
        course_id: 'course-2',
        question: 'Which of the following is a React Hook?',
        options: ['useState', 'componentDidMount', 'render', 'constructor'],
        correct_answer: 0,
        created_at: new Date().toISOString()
      }
    ]
    localStorage.setItem(MCQS_KEY, JSON.stringify(mcqs))
  }

  // Sample certificates
  if (!localStorage.getItem(CERTIFICATES_KEY)) {
    const certificates = [
      {
        id: 'cert-1',
        student_id: 'student-001',
        course_id: 'course-3',
        type: 'gold',
        unique_id: 'AJC-DEMO001',
        issue_date: new Date().toISOString(),
        course: { name: 'Frontend Development' },
        student: { name: 'Test Student' }
      },
      {
        id: 'cert-2',
        student_id: 'student-002',
        course_id: 'course-2',
        type: 'silver',
        unique_id: 'AJC-DEMO002',
        issue_date: new Date().toISOString(),
        course: { name: 'Full Stack Development' },
        student: { name: 'John Doe' }
      },
      {
        id: 'cert-3',
        student_id: 'student-001',
        course_id: 'course-1',
        type: 'participation',
        unique_id: 'AJC-DEMO003',
        issue_date: new Date().toISOString(),
        course: { name: 'Power BI Mastery' },
        student: { name: 'Test Student' }
      }
    ]
    localStorage.setItem(CERTIFICATES_KEY, JSON.stringify(certificates))
  }
}

// Mock API functions
export const getCourses = async () => {
  initializeMockData()
  const courses = JSON.parse(localStorage.getItem(COURSES_KEY) || '[]')
  return { data: courses, error: null }
}

export const createCourse = async (courseData: any) => {
  const courses = JSON.parse(localStorage.getItem(COURSES_KEY) || '[]')
  const newCourse = {
    id: `course-${Date.now()}`,
    ...courseData,
    created_at: new Date().toISOString()
  }
  courses.push(newCourse)
  localStorage.setItem(COURSES_KEY, JSON.stringify(courses))
  return { data: newCourse, error: null }
}

export const getAllUsers = async () => {
  const users = JSON.parse(localStorage.getItem(USERS_KEY) || '[]')
  return { data: users, error: null }
}

export const getCourseTasks = async (courseId: string) => {
  const tasks = JSON.parse(localStorage.getItem(TASKS_KEY) || '[]')
  const courseTasks = tasks.filter((task: any) => task.course_id === courseId)
  return { data: courseTasks, error: null }
}

export const getCourseMCQs = async (courseId: string) => {
  const mcqs = JSON.parse(localStorage.getItem(MCQS_KEY) || '[]')
  const courseMCQs = mcqs.filter((mcq: any) => mcq.course_id === courseId)
  return { data: courseMCQs, error: null }
}

export const submitMCQResult = async (studentId: string, mcqId: string, score: number) => {
  const results = JSON.parse(localStorage.getItem(RESULTS_KEY) || '[]')
  const newResult = {
    id: `result-${Date.now()}`,
    student_id: studentId,
    mcq_id: mcqId,
    score,
    submitted_at: new Date().toISOString()
  }
  results.push(newResult)
  localStorage.setItem(RESULTS_KEY, JSON.stringify(results))
  return { data: newResult, error: null }
}

export const getStudentResults = async (studentId: string) => {
  const results = JSON.parse(localStorage.getItem(RESULTS_KEY) || '[]')
  const studentResults = results.filter((result: any) => result.student_id === studentId)
  return { data: studentResults, error: null }
}

export const getStudentCertificates = async (studentId: string) => {
  const certificates = JSON.parse(localStorage.getItem(CERTIFICATES_KEY) || '[]')
  const studentCertificates = certificates.filter((cert: any) => cert.student_id === studentId)
  return { data: studentCertificates, error: null }
}

export const verifyCertificate = async (certificateId: string) => {
  initializeMockData()
  const certificates = JSON.parse(localStorage.getItem(CERTIFICATES_KEY) || '[]')
  const certificate = certificates.find((cert: any) => cert.unique_id === certificateId)
  return certificate ? { data: certificate, error: null } : { data: null, error: 'Certificate not found' }
}

export const getStudentEnrollments = async (studentId: string) => {
  // Mock enrollments - assume student is enrolled in all courses for demo
  const courses = JSON.parse(localStorage.getItem(COURSES_KEY) || '[]')
  const enrollments = courses.map((course: any) => ({
    id: `enrollment-${course.id}-${studentId}`,
    student_id: studentId,
    course_id: course.id,
    course: course,
    enrolled_at: new Date().toISOString()
  }))
  return { data: enrollments, error: null }
}

export const getCourseLeaderboard = async (courseId: string) => {
  // Mock leaderboard data
  const leaderboard = [
    { student_id: 'student-001', student_name: 'Test Student', rank: 1, total_score: 950, average_score: 95, quiz_count: 10 },
    { student_id: 'student-002', student_name: 'John Doe', rank: 2, total_score: 880, average_score: 88, quiz_count: 10 },
    { student_id: 'admin-001', student_name: 'Rishik Maduri', rank: 3, total_score: 850, average_score: 85, quiz_count: 10 }
  ]
  return { data: leaderboard, error: null }
}

// Additional mock functions for completeness
export const createTask = async (taskData: any) => {
  const tasks = JSON.parse(localStorage.getItem(TASKS_KEY) || '[]')
  const newTask = { id: `task-${Date.now()}`, ...taskData, created_at: new Date().toISOString() }
  tasks.push(newTask)
  localStorage.setItem(TASKS_KEY, JSON.stringify(tasks))
  return { data: newTask, error: null }
}

export const createMCQ = async (mcqData: any) => {
  const mcqs = JSON.parse(localStorage.getItem(MCQS_KEY) || '[]')
  const newMCQ = { id: `mcq-${Date.now()}`, ...mcqData, created_at: new Date().toISOString() }
  mcqs.push(newMCQ)
  localStorage.setItem(MCQS_KEY, JSON.stringify(mcqs))
  return { data: newMCQ, error: null }
}

export const createCertificate = async (certData: any) => {
  const certificates = JSON.parse(localStorage.getItem(CERTIFICATES_KEY) || '[]')
  const newCert = {
    id: `cert-${Date.now()}`,
    ...certData,
    unique_id: `AJC-${Date.now()}`,
    issue_date: new Date().toISOString()
  }
  certificates.push(newCert)
  localStorage.setItem(CERTIFICATES_KEY, JSON.stringify(certificates))
  return { data: newCert, error: null }
}

export const updateUserRole = async (userId: string, role: string) => {
  const users = JSON.parse(localStorage.getItem(USERS_KEY) || '[]')
  const userIndex = users.findIndex((user: any) => user.id === userId)
  if (userIndex !== -1) {
    users[userIndex].role = role
    localStorage.setItem(USERS_KEY, JSON.stringify(users))
  }
  return { data: users[userIndex], error: null }
}
