import { Alert, AlertDescription } from "@/components/ui/alert"
import { Info } from "lucide-react"

export function MockModeNotification() {
  return (
    <Alert className="mb-4 border-blue-200 bg-blue-50">
      <Info className="h-4 w-4 text-blue-600" />
      <AlertDescription className="text-blue-800">
        <strong>Demo Mode:</strong> You're using the AJC Platform in demo mode with mock data. 
        All features are functional for testing. 
        <br />
        <strong>Demo Accounts:</strong> Admin: <EMAIL> (password: admin123) | 
        Student: <EMAIL> (password: student123)
      </AlertDescription>
    </Alert>
  )
}
