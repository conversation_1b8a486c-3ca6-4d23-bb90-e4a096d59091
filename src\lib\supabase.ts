import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  name: string
  email: string
  role: 'admin' | 'student' | 'faculty'
  created_at: string
  updated_at: string
}

export interface Course {
  id: string
  name: string
  description: string
  price: number
  faculty_id: string
  created_at: string
  updated_at: string
  faculty?: User
}

export interface Enrollment {
  id: string
  student_id: string
  course_id: string
  status: 'active' | 'completed' | 'dropped'
  enrolled_at: string
  student?: User
  course?: Course
}

export interface Task {
  id: string
  course_id: string
  title: string
  description: string
  deadline: string
  created_at: string
  course?: Course
}

export interface MCQ {
  id: string
  course_id: string
  question: string
  options: string[]
  correct_answer: number
  created_at: string
  course?: Course
}

export interface Result {
  id: string
  student_id: string
  mcq_id: string
  score: number
  submitted_at: string
  student?: User
  mcq?: MCQ
}

export interface Certificate {
  id: string
  student_id: string
  course_id: string
  type: 'gold' | 'silver' | 'participation'
  unique_id: string
  issue_date: string
  student?: User
  course?: Course
}

// Auth helper functions
export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()
  return user
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  return { error }
}

export const isAdmin = (email: string) => {
  return email === import.meta.env.VITE_ADMIN_EMAIL
}
