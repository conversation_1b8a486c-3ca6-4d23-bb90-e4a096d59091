import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react'

interface UserProfile {
  id: string
  name: string
  email: string
  role: 'admin' | 'student' | 'faculty'
  created_at: string
}

interface AuthContextType {
  user: { id: string; email: string } | null
  userProfile: UserProfile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signUp: (email: string, password: string, name: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  isAuthenticated: boolean
  isAdminUser: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Mock user storage for testing
const MOCK_USERS_KEY = 'ajc_mock_users'
const CURRENT_USER_KEY = 'ajc_current_user'

interface MockUser {
  id: string
  email: string
  password: string
  name: string
  role: 'admin' | 'student' | 'faculty'
  created_at: string
}

// Initialize with admin user and sample students
const initializeMockUsers = () => {
  const existingUsers = localStorage.getItem(MOCK_USERS_KEY)
  if (!existingUsers) {
    const defaultUsers: MockUser[] = [
      {
        id: 'admin-001',
        email: '<EMAIL>',
        password: 'admin123',
        name: 'Rishik Maduri',
        role: 'admin',
        created_at: new Date().toISOString()
      },
      {
        id: 'student-001',
        email: '<EMAIL>',
        password: 'student123',
        name: 'Test Student',
        role: 'student',
        created_at: new Date().toISOString()
      },
      {
        id: 'student-002',
        email: '<EMAIL>',
        password: 'password123',
        name: 'John Doe',
        role: 'student',
        created_at: new Date().toISOString()
      }
    ]
    localStorage.setItem(MOCK_USERS_KEY, JSON.stringify(defaultUsers))
  }
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<{ id: string; email: string } | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  const adminEmail = import.meta.env.VITE_ADMIN_EMAIL || '<EMAIL>'
  const isAdminUser = userProfile?.email === adminEmail
  const isAuthenticated = !!user

  useEffect(() => {
    // Initialize mock users
    initializeMockUsers()
    
    // Check for existing session
    const currentUser = localStorage.getItem(CURRENT_USER_KEY)
    if (currentUser) {
      const userData = JSON.parse(currentUser)
      setUser({ id: userData.id, email: userData.email })
      setUserProfile(userData)
    }
    
    setLoading(false)
  }, [])

  const signIn = useCallback(async (email: string, password: string): Promise<{ error: any }> => {
    try {
      // Add a small delay to simulate network request and prevent UI hanging
      await new Promise(resolve => setTimeout(resolve, 300))

      const users: MockUser[] = JSON.parse(localStorage.getItem(MOCK_USERS_KEY) || '[]')
      const foundUser = users.find(u => u.email === email && u.password === password)

      if (!foundUser) {
        return { error: { message: 'Invalid email or password' } }
      }

      const userData = {
        id: foundUser.id,
        name: foundUser.name,
        email: foundUser.email,
        role: foundUser.role,
        created_at: foundUser.created_at
      }

      setUser({ id: foundUser.id, email: foundUser.email })
      setUserProfile(userData)
      localStorage.setItem(CURRENT_USER_KEY, JSON.stringify(userData))

      return { error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      return { error: { message: 'Login failed' } }
    }
  }, [])

  const signUp = useCallback(async (email: string, password: string, name: string): Promise<{ error: any }> => {
    try {
      // Add a small delay to simulate network request
      await new Promise(resolve => setTimeout(resolve, 300))

      const users: MockUser[] = JSON.parse(localStorage.getItem(MOCK_USERS_KEY) || '[]')

      // Check if user already exists
      if (users.find(u => u.email === email)) {
        return { error: { message: 'User already exists' } }
      }

      const newUser: MockUser = {
        id: `user-${Date.now()}`,
        email,
        password,
        name,
        role: email === adminEmail ? 'admin' : 'student',
        created_at: new Date().toISOString()
      }

      users.push(newUser)
      localStorage.setItem(MOCK_USERS_KEY, JSON.stringify(users))

      const userData = {
        id: newUser.id,
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
        created_at: newUser.created_at
      }

      setUser({ id: newUser.id, email: newUser.email })
      setUserProfile(userData)
      localStorage.setItem(CURRENT_USER_KEY, JSON.stringify(userData))

      return { error: null }
    } catch (error) {
      console.error('Sign up error:', error)
      return { error: { message: 'Signup failed' } }
    }
  }, [adminEmail])

  const signOut = useCallback(async (): Promise<void> => {
    setUser(null)
    setUserProfile(null)
    localStorage.removeItem(CURRENT_USER_KEY)
  }, [])

  const value: AuthContextType = useMemo(() => ({
    user,
    userProfile,
    loading,
    signIn,
    signUp,
    signOut,
    isAuthenticated,
    isAdminUser
  }), [user, userProfile, loading, signIn, signUp, signOut, isAuthenticated, isAdminUser])

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
