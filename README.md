# 🎓 AJC Internship Platform

A comprehensive full-stack learning management system built with React, Supabase, and modern web technologies. Features role-based authentication, course management, interactive assessments, and certificate verification.

## ✨ Features

### 🔐 Authentication & Authorization
- **Role-based access control** (Admin/Student)
- **Secure authentication** with <PERSON><PERSON><PERSON> Auth
- **Protected routes** with automatic redirects
- **Admin privileges** for <EMAIL>

### 👨‍💼 Admin Dashboard
- **Course Management**: Create, edit, delete courses
- **User Management**: View and manage student/faculty roles
- **Content Creation**: Tasks, MCQs, and assessments
- **Certificate Generation**: Issue certificates with unique IDs
- **Analytics**: Track student progress and performance

### 👨‍🎓 Student Dashboard
- **Course Enrollment**: View and access enrolled courses
- **Interactive Learning**: Tasks, quizzes, and progress tracking
- **Leaderboards**: Real-time rankings and competition
- **Certificates**: View and download earned certificates
- **Progress Tracking**: Monitor learning journey

### 🎓 Certificate Verification
- **Public verification** system for certificate authenticity
- **Unique certificate IDs** with database lookup
- **Instant verification** results
- **Professional certificate display**

### 🎨 Modern UI/UX
- **Responsive design** for all devices
- **Sidebar navigation** with collapsible menu
- **Course cards** with intuitive icons
- **Notification system** with badge indicators
- **shadcn/ui components** with Tailwind CSS

## 🚀 Tech Stack

- **Frontend**: React 18 + Vite + TypeScript
- **UI Framework**: shadcn/ui + Tailwind CSS
- **Backend**: Supabase (Database + Auth + Storage)
- **State Management**: React Context + Hooks
- **Routing**: React Router v6
- **Icons**: Lucide React
- **Deployment**: Vercel/Netlify ready

## 📦 Quick Start

1. **Install dependencies**
```bash
npm install
```

2. **Environment setup**
```bash
# Copy and edit environment file
cp .env.local.example .env.local
```

3. **Database setup**
- Create a Supabase project at [supabase.com](https://supabase.com)
- Run `database-schema.sql` in SQL Editor
- Run `database-seed.sql` for sample data

4. **Start development**
```bash
npm run dev
```

## 🔧 Configuration

### Environment Variables
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_ADMIN_EMAIL=<EMAIL>
```

## 🧪 Testing

### Demo Accounts
- **Admin**: <EMAIL>
- **Student**: Any other email address

### Sample Certificate IDs
- AJC-DEMO001, AJC-DEMO002, AJC-DEMO003

## 📚 Documentation

- **Setup Guide**: `SETUP_GUIDE.md`
- **Testing Checklist**: `TESTING_CHECKLIST.md`
- **Database Schema**: `database-schema.sql`

## 🚀 Deployment

Ready for deployment on Vercel, Netlify, or any static hosting provider.

---

**Built with ❤️ for AJC Internship Program**

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/2c7ab8f5-faa3-447f-a514-583b075c5f5a) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
