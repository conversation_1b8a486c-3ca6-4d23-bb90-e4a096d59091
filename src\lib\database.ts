import { supabase, Course, Enrollment, Task, MCQ, Result, Certificate, User } from './supabase'

// Course operations
export const getCourses = async () => {
  const { data, error } = await supabase
    .from('courses')
    .select(`
      *,
      faculty:users(id, name, email)
    `)
    .order('created_at', { ascending: false })
  
  return { data, error }
}

export const createCourse = async (course: Omit<Course, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('courses')
    .insert([course])
    .select()
    .single()
  
  return { data, error }
}

export const updateCourse = async (id: string, updates: Partial<Course>) => {
  const { data, error } = await supabase
    .from('courses')
    .update(updates)
    .eq('id', id)
    .select()
    .single()
  
  return { data, error }
}

export const deleteCourse = async (id: string) => {
  const { error } = await supabase
    .from('courses')
    .delete()
    .eq('id', id)
  
  return { error }
}

// Enrollment operations
export const getStudentEnrollments = async (studentId: string) => {
  const { data, error } = await supabase
    .from('enrollments')
    .select(`
      *,
      course:courses(*)
    `)
    .eq('student_id', studentId)
    .eq('status', 'active')
  
  return { data, error }
}

export const enrollStudent = async (studentId: string, courseId: string) => {
  const { data, error } = await supabase
    .from('enrollments')
    .insert([{
      student_id: studentId,
      course_id: courseId,
      status: 'active'
    }])
    .select()
    .single()
  
  return { data, error }
}

export const getCourseEnrollments = async (courseId: string) => {
  const { data, error } = await supabase
    .from('enrollments')
    .select(`
      *,
      student:users(id, name, email)
    `)
    .eq('course_id', courseId)
  
  return { data, error }
}

// Task operations
export const getCourseTasks = async (courseId: string) => {
  const { data, error } = await supabase
    .from('tasks')
    .select('*')
    .eq('course_id', courseId)
    .order('deadline', { ascending: true })
  
  return { data, error }
}

export const createTask = async (task: Omit<Task, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('tasks')
    .insert([task])
    .select()
    .single()
  
  return { data, error }
}

// MCQ operations
export const getCourseMCQs = async (courseId: string) => {
  const { data, error } = await supabase
    .from('mcqs')
    .select('*')
    .eq('course_id', courseId)
    .order('created_at', { ascending: false })
  
  return { data, error }
}

export const createMCQ = async (mcq: Omit<MCQ, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('mcqs')
    .insert([mcq])
    .select()
    .single()
  
  return { data, error }
}

// Result operations
export const submitMCQResult = async (studentId: string, mcqId: string, score: number) => {
  const { data, error } = await supabase
    .from('results')
    .upsert([{
      student_id: studentId,
      mcq_id: mcqId,
      score
    }])
    .select()
    .single()
  
  return { data, error }
}

export const getStudentResults = async (studentId: string, courseId?: string) => {
  let query = supabase
    .from('results')
    .select(`
      *,
      mcq:mcqs(
        *,
        course:courses(*)
      )
    `)
    .eq('student_id', studentId)
  
  if (courseId) {
    query = query.eq('mcq.course_id', courseId)
  }
  
  const { data, error } = await query
  return { data, error }
}

// Certificate operations
export const createCertificate = async (certificate: Omit<Certificate, 'id' | 'unique_id' | 'issue_date'>) => {
  const { data, error } = await supabase
    .from('certificates')
    .insert([certificate])
    .select()
    .single()
  
  return { data, error }
}

export const getStudentCertificates = async (studentId: string) => {
  const { data, error } = await supabase
    .from('certificates')
    .select(`
      *,
      course:courses(*)
    `)
    .eq('student_id', studentId)
    .order('issue_date', { ascending: false })
  
  return { data, error }
}

export const verifyCertificate = async (uniqueId: string) => {
  const { data, error } = await supabase
    .from('certificates')
    .select(`
      *,
      student:users(name),
      course:courses(name)
    `)
    .eq('unique_id', uniqueId)
    .single()
  
  return { data, error }
}

// User operations
export const getAllUsers = async () => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .order('created_at', { ascending: false })
  
  return { data, error }
}

export const updateUserRole = async (userId: string, role: 'admin' | 'student' | 'faculty') => {
  const { data, error } = await supabase
    .from('users')
    .update({ role })
    .eq('id', userId)
    .select()
    .single()
  
  return { data, error }
}

// Leaderboard operations
export const getCourseLeaderboard = async (courseId: string) => {
  const { data, error } = await supabase
    .from('results')
    .select(`
      student_id,
      student:users(name),
      score,
      mcq:mcqs!inner(course_id)
    `)
    .eq('mcq.course_id', courseId)
  
  if (error) return { data: null, error }
  
  // Calculate total scores per student
  const leaderboard = data.reduce((acc: any[], result: any) => {
    const existingStudent = acc.find(s => s.student_id === result.student_id)
    if (existingStudent) {
      existingStudent.total_score += result.score
      existingStudent.quiz_count += 1
    } else {
      acc.push({
        student_id: result.student_id,
        student_name: result.student.name,
        total_score: result.score,
        quiz_count: 1
      })
    }
    return acc
  }, [])
  
  // Sort by total score and add rank
  leaderboard.sort((a, b) => b.total_score - a.total_score)
  leaderboard.forEach((student, index) => {
    student.rank = index + 1
    student.average_score = Math.round(student.total_score / student.quiz_count)
  })
  
  return { data: leaderboard, error: null }
}
