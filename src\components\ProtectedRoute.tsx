import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/MockAuthContext'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAdmin?: boolean
  requireStudent?: boolean
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireAdmin = false, 
  requireStudent = false 
}) => {
  const { isAuthenticated, isAdminUser, userProfile, loading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        navigate('/login')
        return
      }

      if (requireAdmin && !isAdminUser) {
        navigate('/student') // Redirect non-admin to student dashboard
        return
      }

      if (requireStudent && isAdminUser) {
        navigate('/admin') // Redirect admin to admin dashboard
        return
      }
    }
  }, [isAuthenticated, isAdminUser, loading, navigate, requireAdmin, requireStudent])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  if (requireAdmin && !isAdminUser) {
    return null
  }

  if (requireStudent && isAdminUser) {
    return null
  }

  return <>{children}</>
}

export default ProtectedRoute
